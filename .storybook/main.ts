import type { StorybookConfig } from "@storybook/react-vite";

const config: StorybookConfig = {
  // stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  stories: [
    "../packages/**/*.mdx",
    "../packages/**/*.stories.@(js|jsx|mjs|ts|tsx)",
  ],
  addons: [
    "@storybook/addon-onboarding",
    "@storybook/addon-essentials",
    "@chromatic-com/storybook",
    "@storybook/addon-interactions",
  ],
  framework: {
    name: "@storybook/react-vite",
    options: {},
  },
  // 添加 viteFinal 配置来注入 CDN 资源
  viteFinal: async (config) => {
    config.plugins = config.plugins || [];
    config.plugins.push({
      name: "inject-cdn-resources",
      transformIndexHtml(html) {
        return html.replace(
          /<\/body>/,
          `
            <!-- 在这里添加你的 CDN 资源 -->
            <script src="https://cdn.ainvest.com/frontResources/lib/selfstock/index_1.0.0.js"></script>
            <script src="https://cdn.ainvest.com/frontResources/lib/axios.min.js"></script>
            </body>
            
          `
        );
      },
    });

    return config;
  },
};
export default config;
