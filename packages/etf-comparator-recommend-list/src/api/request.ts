import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * 通用 GET 请求封装
 * @param url 请求地址
 * @param config axios 配置
 * @returns Promise<AxiosResponse>
 * @example
 *   requestGet('/api/user', { timeout: 3000 }).then(resp => console.log(resp.data));
 */
export async function requestGet(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
  return axios.get(url, config);
}

/**
 * 通用 POST 请求封装
 * @param url 请求地址
 * @param data 请求体
 * @param config axios 配置
 * @returns Promise<AxiosResponse>
 * @example
 *   requestPost('/api/submit', { name: '张三' }, { timeout: 3000 }).then(resp => alert(resp.data));
 */
export async function requestPost(
  url: string,
  data?: Record<string, any>,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse> {
  return axios.post(url, data, config);
}