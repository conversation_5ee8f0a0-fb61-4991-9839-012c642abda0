/**
 * RecommendCard.tsx
 * 推荐卡片组件 - 展示单个推荐项目的完整信息，包括主题信息、涨跌幅、描述文案和ETF按钮列表
 *
 * 功能特性:
 * 1. 展示主题名称、涨跌幅、描述文案（最多3行，超出部分截断）
 * 2. 提供一个加入对比功能按钮
 * 3. ETF按钮列表支持点击跳转至ETF分时页
 */

import React from "react";
import { EtfButton } from "./EtfButton";

import {
  PK_BADGE_TEXT,
  COMPARE_BUTTON_TEXT,
  ETF_BUTTON_GAP_SPACING,
} from "../constants/index";

// 推荐卡片组件Props接口
interface RecommendCardProps {
  title: string; // 主题名称
  changeRate: string; // 涨跌幅
  description: string; // 描述文案
  etfList: Array<{ name: string }>; // ETF列表数据
  onEtfClick?: (etfName: string) => void; // ETF点击回调
  onCompareClick?: () => void; // 加入对比点击回调
}

// 推荐卡片组件定义
export const RecommendCard: React.FC<RecommendCardProps> = ({
  title,
  changeRate,
  description,
  etfList = [],
  onEtfClick,
  onCompareClick,
}) => {
  // 根据涨跌幅判断颜色样式
  const getChangeRateColor = (rate: string): string => {
    const trimmedRate = rate.trim();
    if (trimmedRate.startsWith("+")) {
      return "text-red-600"; // 上涨红色
    } else if (trimmedRate.startsWith("-")) {
      return "text-green-600"; // 下跌绿色
    } else {
      // 如果没有符号，尝试解析数值
      const numValue = parseFloat(trimmedRate.replace("%", ""));
      if (numValue > 0) {
        return "text-red-600";
      } else if (numValue < 0) {
        return "text-green-600";
      }
      return "text-gray-600"; // 默认颜色
    }
  };

  const changeRateColor = getChangeRateColor(changeRate);

  // 根据ETF数量计算布局间距
  const getEtfButtonSpacing = () => {
    return ETF_BUTTON_GAP_SPACING; // 统一使用相同间距
  };

  return (
    <div className="w-full bg-background-layer2 rounded-md py-3 px-2.5">
      <div className="w-full flex justify-between">
        <div className="flex">
          {/* 主题名称 */}
          <div className=" flex justify-start items-center text-gray-900 text-base font-medium">
            {title}
          </div>

          {/* 涨跌幅 */}
          <div
            className={` flex justify-start items-center ${changeRateColor} text-base font-medium ml-2`}
          >
            {changeRate}
          </div>
        </div>

        {/* 加入对比按钮 */}
        <button
          className="flex items-center text-right justify-start text-red-600 text-sm font-normal cursor-pointer hover:opacity-80"
          onClick={onCompareClick}
        >
          <img
            className="w-4 h-4"
            src="https://s.thsi.cn/staticS3/mobileweb-upload-static-server.img/offline/pkg/a8675102-7a6d-4bf4-8faa-db91dab72dba.png"
            alt=""
          />
          {COMPARE_BUTTON_TEXT}
        </button>
      </div>

      {/* 描述文案 - 支持最多3行显示，超出部分截断 */}
      <div
        className="w-80  text-gray-600 text-sm font-normal overflow-hidden"
        style={{
          display: "-webkit-box",
          WebkitLineClamp: 3,
          WebkitBoxOrient: "vertical",
          lineHeight: "1.4",
          height: "60px", // 约3行的高度
        }}
      >
        {description}
      </div>

      {/* ETF按钮列表 */}
      <div className={`l flex ${getEtfButtonSpacing()}`}>
        {etfList.map((etf, index) => {
          // 根据索引位置决定是否显示PK标签
          const showPkBadge = etfList.length > 1 && index > 0;

          return (
            <div key={index} className="relative">
              <EtfButton
                name={etf.name}
                isSelected={false}
                onClick={() => onEtfClick?.(etf.name)}
              />

              {/* PK标签 - 在第二个和第三个ETF按钮上显示 */}
              {showPkBadge && (
                <div
                  className="h-2.5 p-px absolute bg-red-600 rounded-sm inline-flex justify-center items-center gap-2.5"
                  style={{
                    top: "9px",
                    left: "-10px",
                  }}
                >
                  <div className="flex justify-center items-center text-white text-[9px] font-medium leading-[9px]">
                    {PK_BADGE_TEXT}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
