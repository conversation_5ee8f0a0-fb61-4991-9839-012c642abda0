/**
 * EtfButton.tsx
 * ETF按钮组件 - 展示单个ETF产品信息，支持点击跳转至ETF分时页
 * 
 * 功能特性:
 * 1. 展示ETF名称
 * 2. 支持选中与未选中的视觉状态
 * 3. 点击按钮触发跳转回调
 */

import React from 'react';

// ETF按钮组件Props接口
interface EtfButtonProps {
  name: string; // ETF名称
  isSelected?: boolean; // 是否被选中状态，默认false
  onClick?: () => void; // 点击回调函数
}

// ETF按钮组件定义
export const EtfButton: React.FC<EtfButtonProps> = ({
  name,
  isSelected = false,
  onClick
}) => {
  const handleClick = () => {
    onClick?.();
  };

  return (
    <div className="relative">
      {/* 基础按钮样式 - 白色背景，圆角 */}
      <div 
        className={`w-40 h-8 bg-white rounded-md ${
          onClick ? 'cursor-pointer' : 'cursor-default'
        }`} 
        onClick={handleClick}
      >
        <div className="flex items-center justify-center h-full">
          <span className="text-sm text-gray-800">{name}</span>
        </div>
      </div>
      
      {/* 选中状态 - 红色边框和背景overlay */}
      {isSelected && (
        <div className="w-40 h-8 absolute top-0 left-0 bg-red-600/20 border border-red-600 rounded-md pointer-events-none" />
      )}
    </div>
  );
};