import React from "react";

import {
  RECOMMEND_CARD_VERTICAL_SPACING,
  DEFAULT_RECOMMEND_TITLE,
  RECOMMEND_CARD_TOP_SPACING,
} from "./constants/index.ts";

import { RecommendCard } from "./components/RecommendCard";

/**
 * EtfComparatorRecommendList.tsx
 * ETF对比推荐列表组件 - 展示热门推荐的ETF产品列表，支持多个ETF产品的对比功能
 *
 * 功能特性:
 * 1. 显示推荐列表标题
 * 2. 动态渲染推荐卡片列表
 * 3. 支持ETF点击跳转和加入对比功能
 */

// 推荐列表项数据接口
interface RecommendListItem {
  title: string; // 主题名称
  changeRate: string; // 涨跌幅
  description: string; // 描述文案
  etfList: Array<{ name: string }>; // ETF列表数据
}

// ETF对比推荐列表组件Props接口
interface EtfComparatorRecommendListProps {
  title?: string; // 列表标题
  recommendList: Array<RecommendListItem>; // 推荐列表数据
  onEtfClick?: (etfName: string) => void; // ETF点击回调
  onCompareClick?: () => void; // 加入对比点击回调
}

// ETF对比推荐列表组件定义
export const EtfComparatorRecommendList: React.FC<
  EtfComparatorRecommendListProps
> = ({
  title = DEFAULT_RECOMMEND_TITLE,
  recommendList = [],
  onEtfClick,
  onCompareClick,
}) => {
  return (
    <div className="w-full bg-foreground-layer1 flex flex-col items-center px-2.5 py-3 rounded-md">
      {/* 标题区域 */}
      <div className="w-full justify-start text-text-primary text-lg font-medium leading-snug mb-3.5">
        {title}
      </div>
      {/* 推荐卡片列表 */}
      {recommendList.map((item, index) => (
        <RecommendCard
          key={index}
          title={item.title}
          changeRate={item.changeRate}
          description={item.description}
          etfList={item.etfList}
          onEtfClick={onEtfClick}
          onCompareClick={onCompareClick}
        />
      ))}
    </div>
  );
};
