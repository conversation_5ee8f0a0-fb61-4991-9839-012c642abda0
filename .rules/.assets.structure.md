# Asset 资源使用指南

## 资源存储规范

**资源文件组织：**
- SVG资源存放在 `assets/svg.ts` 文件中
- PNG资源存放在 `assets/png.ts` 文件中
- 资源以常量字典形式导出，key为资源ID，value为资源数据

**资源数据格式：**
- SVG类型：value为SVG字符串内容
- PNG类型：value为Base64编码的图片数据

## 资源文件结构示例

**assets/svg.ts：**
```typescript
export const SVG_ASSETS = {
  'svg_icon_user_24_abc123': '<svg width="24" height="24" viewBox="0 0 24 24"><path d="..."/></svg>',
  'svg_logo_brand_32_def456': '<svg width="32" height="32" viewBox="0 0 32 32"><path d="..."/></svg>',
  'svg_arrow_right_16_ghi789': '<svg width="16" height="16" viewBox="0 0 16 16"><path d="..."/></svg>'
};
```

**assets/png.ts：**
```typescript
export const PNG_ASSETS = {
  'png_photo_avatar_128_abc123': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  'png_product_main_400_def456': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  'png_background_hero_1200_ghi789': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
};
```

## SVG 资源使用指南

### SVG 替换方式说明
**替换规则：**
- 直接将SVG字符串内容替换`<asset-placeholder>`标签
- 可以使用dangerouslySetInnerHTML或直接内联
- 保持原有的样式类名和属性

### SVG 使用示例（React + TailwindCSS）

**原始placeholder：**
```jsx
<div className="text-gray-600">
  <asset-placeholder 
    type="svg"
    asset-id="svg_icon_user_24_abc123"
    placeholder="__ASSET_SVG_icon_user_24_abc123_001__"
    width="24"
    height="24"
    alt="用户图标"
    class="text-gray-600"
  />
</div>
```

**替换后的代码：**
```jsx
import { SVG_ASSETS } from 'assets/svg';

// 方式1：使用dangerouslySetInnerHTML（推荐）
<div className="text-gray-600">
  <div 
    className="w-6 h-6 text-gray-600" 
    dangerouslySetInnerHTML={{ __html: SVG_ASSETS['svg_icon_user_24_abc123'] }}
  />
</div>

// 方式2：创建SVG组件（更好的内联方式）
const IconUser = () => (
  <div 
    className="w-6 h-6 text-gray-600" 
    dangerouslySetInnerHTML={{ __html: SVG_ASSETS['svg_icon_user_24_abc123'] }}
  />
);

// 使用SVG组件
<div className="text-gray-600">
  <IconUser />
</div>

// 方式3：使用自定义Hook（最佳实践）
const useSVG = (assetId: string) => {
  return SVG_ASSETS[assetId] || '';
};

const MyComponent = () => {
  const userIcon = useSVG('svg_icon_user_24_abc123');
  
  return (
    <div className="text-gray-600">
      <div 
        className="w-6 h-6 text-gray-600"
        dangerouslySetInnerHTML={{ __html: userIcon }}
      />
    </div>
  );
};
```

### SVG 资源文件示例
```typescript
// assets/svg.ts
export const SVG_ASSETS = {
  'svg_icon_user_24_abc123': '<svg width="24" height="24" viewBox="0 0 24 24"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="currentColor"/></svg>',
  'svg_logo_brand_32_def456': '<svg width="32" height="32" viewBox="0 0 32 32"><path d="..." fill="currentColor"/></svg>',
  'svg_arrow_right_16_ghi789': '<svg width="16" height="16" viewBox="0 0 16 16"><path d="..." fill="currentColor"/></svg>'
};
```

### SVG 最佳实践
1. **统一使用字典**：始终从`SVG_ASSETS`字典获取SVG内容，避免硬编码
2. **样式继承**：使用`fill="currentColor"`让SVG继承父元素文字颜色
3. **组件化**：将常用SVG封装成可复用组件
4. **自定义Hook**：使用Hook封装SVG获取逻辑，便于统一管理
5. **类型安全**：使用TypeScript确保asset-id的正确性
6. **性能优化**：小图标优先使用SVG格式

### SVG 进阶用法
```jsx
// 创建通用SVG组件
interface SVGIconProps {
  assetId: string;
  className?: string;
  size?: number;
}

const SVGIcon: React.FC<SVGIconProps> = ({ assetId, className = '', size = 24 }) => {
  const svgContent = SVG_ASSETS[assetId];
  
  if (!svgContent) {
    console.warn(`SVG asset not found: ${assetId}`);
    return null;
  }
  
  return (
    <div 
      className={`inline-block ${className}`}
      style={{ width: size, height: size }}
      dangerouslySetInnerHTML={{ __html: svgContent }}
    />
  );
};

// 使用通用组件
<SVGIcon assetId="svg_icon_user_24_abc123" className="text-blue-500" size={32} />
```

---

## PNG 资源使用指南

### PNG 替换方式说明
**替换规则：**
- 将PNG数据URL替换到img标签的src属性
- 保持原有的尺寸和样式属性
- 转换class为className（React）

### PNG 使用示例（React + TailwindCSS）

**原始placeholder：**
```jsx
<div className="w-32 h-32">
  <asset-placeholder 
    type="png"
    asset-id="png_photo_avatar_128_abc123"
    placeholder="__ASSET_PNG_photo_avatar_128_abc123_002__"
    width="128"
    height="128"
    alt="用户头像"
    class="rounded-full object-cover"
  />
</div>
```

**替换后的代码：**
```jsx
import { PNG_ASSETS } from 'assets/png';

<div className="w-32 h-32">
  <img 
    src={PNG_ASSETS['png_photo_avatar_128_abc123']}
    width="128"
    height="128"
    alt="用户头像"
    className="rounded-full object-cover"
  />
</div>
```

### PNG 资源文件示例
```typescript
// assets/png.ts
export const PNG_ASSETS = {
  'png_photo_avatar_128_abc123': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVHic7d...',
  'png_product_main_400_def456': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAYAAACAvzbMAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAsQAAALEBxi9KAQAABYBQVR0eF...',
  'png_background_hero_1200_ghi789': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABLAAAASwCAYAAADIbGlpAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAYgAAAGIBTnSmnAAABkpJREFUeJzt...'
};
```

### PNG 最佳实践
1. **尺寸优化**：提供合适的width和height避免布局偏移
2. **懒加载**：大图片考虑使用loading="lazy"
3. **响应式**：使用object-fit控制图片适应方式
4. **回退方案**：提供有意义的alt文本

## 资源类型识别规则

### SVG资源识别
**识别特征：**
- `type="svg"`
- `asset-id`以`svg_`开头
- `placeholder`包含`ASSET_SVG`

**SVG资源标签示例：**
```jsx
<asset-placeholder 
  type="svg"                                    // 关键标识
  asset-id="svg_icon_user_24_abc123"           // SVG资源ID
  placeholder="__ASSET_SVG_icon_user_24_abc123_001__"  // SVG占位符
  width="24"
  height="24"
  alt="用户图标"
  class="text-gray-600"
/>
```

### PNG资源识别
**识别特征：**
- `type="png"`
- `asset-id`以`png_`开头
- `placeholder`包含`ASSET_PNG`

**PNG资源标签示例：**
```jsx
<asset-placeholder 
  type="png"                                    // 关键标识
  asset-id="png_photo_avatar_128_abc123"       // PNG资源ID
  placeholder="__ASSET_PNG_photo_avatar_128_abc123_002__"  // PNG占位符
  width="128"
  height="128"
  alt="用户头像"
  class="rounded-full object-cover"
/>
```

## 核心规则总结

### 资源组织规则
1. **SVG资源**：存储在`assets/svg.ts`的`SVG_ASSETS`字典中
2. **PNG资源**：存储在`assets/png.ts`的`PNG_ASSETS`字典中
3. **字典结构**：`{ 'asset-id': 'asset-data' }`

### 替换规则
1. **资源类型判断**：通过`type`属性识别（`svg`或`png`）
2. **资源定位**：通过`asset-id`在对应字典中查找
3. **SVG替换**：字符串内容直接替换placeholder标签
4. **PNG替换**：Data URL替换到img标签的src属性

### 导入方式
```typescript
// SVG资源导入
import { SVG_ASSETS } from 'assets/svg';

// PNG资源导入
import { PNG_ASSETS } from 'assets/png';

// 使用方式
const svgContent = SVG_ASSETS['svg_icon_user_24_abc123'];
const pngDataUrl = PNG_ASSETS['png_photo_avatar_128_abc123'];
```

### 最佳实践
1. **保持一致性**：统一使用字典方式访问资源
2. **类型安全**：使用TypeScript确保资源ID的正确性
3. **性能优化**：小图标使用SVG，照片使用PNG
4. **可维护性**：资源ID命名要语义化且规范