# 组件目录

## 注意事项

1. 目录结构

- 组件规范目录结构如下：

```
组件
└─ src             # 组件源码核心目录
   ├─ index.css    # 组件样式文件（一般不用）
   ├─ index.stories.ts    # 组件 storybook 示例
   ├─ index.tsx    # 组件核心入口文件
   └─ components   # 子组件目录
        └─ 子组件 A  # 子组件 A 目录
           ├─ index.tsx    # 子组件 A 入口文件
           └─ index.css    # 子组件 A 样式文件（一般不用）
   └─ utils
        └─ index.ts    # 组件工具函数
   └─ constants
        └─ index.ts    # 组件常量
├─ index.ts
├─ matrix.config.js
├─ package.json
├─ README.md
├─ tailwind.config.js
└─ tsconfig.json
```

2. 技术栈与代码规范要求

- React 版本：使用 React 技术栈，版本为 `React 18.2`。
- 语法要求：使用 TypeScript 编写组件代码。以 `.tsx` / `.ts` 为文件结尾。
- 组件类型：优先使用函数式组件，不要使用 React.memo。
- 命名规范：组件名称必须以大写字母开头，并遵循驼峰命名法。
- HTML 处理：使用面向 SEO 友好、有语义的 HTML 标签。
- 样式处理：
  - 使用 TailwindCSS 实现样式，或采用内联 css style 样式，涉及逻辑判断的类名提前处理。不要设置字体，字体大小使用 px 为单位。
  - “涨跌平”对应的字体颜色或者背景色，直接添加对应的 style 样式，上涨使用 color-price-up 变量，下跌使用 color-price-down，平使用 color-price-even。
  - 确保样式支持支持移动端正常展示、支持响应式设计，样式适配不同设备和屏幕尺寸。比如最外层元素宽度不要限制固定宽度，可以用响应式单位。
- 文本处理：组件涉及的静态文本不要写死，可通过 props 配置并设置默认值。
- props 设计：组件的 props 参数名必须具有语义化，避免使用 id、data、value、type 等模糊不清的命名。
  - 组件需要暴露 props 属性，以提供给编辑器分析组件入参信息，但请注意不要引入`prop-types`以及保证遵循以下格式，如：

```js
// ...

// props用于描述组件的入参信息，需要严格遵循{ [propName: string]: { type: string, default?: any }}格式
export const props = {
  // props属性
  text: {
    type: 'string', // 属性类型，{string}，boolean|string|number|object|array|date
    default: 'button', // 默认值，非必需
  },
  showIcon: {
    type: 'boolean', // 属性类型，{string}，boolean|string|number|object|array|date
    default: false, // 默认值，非必需
  },
};
// ...
```

- props 及数据处理：做好兜底和容错处理，比如数组遍历前要进行空值判断。

- 组件拆分标准规则
  - 当组件不复杂或组件间涉及联动、数据状态共享，组件合放在一块、不做组件/文件拆分，直接在 `src/index.tsx` 中实现；
  - 当组件比较复杂，需要做结构化拆分或有“重复“内容、存在可”抽象“且能复用的组件，可以单独拆分出组件，在 `src/components` 目录下新建子组件目录；

- demo 编写规范：
  - 组件的 demo 示例必须使用 storybook 进行编写，根据组件在不同场景下的状态设计不同的 story，每个组件必须至少包含一个 demo 示例，写在 index.stories.ts 中。

3. 依赖管理

- 外部依赖：组件内部暂时不引入除 React 外的其他依赖，包括 CSS 文件。
- 数据与请求：
  - 数据信息通过 props 导入，暂时不要在组件中进行请求处理。
  - 请求处理使用 `window.axios` 方法，功能与 `axios` 相同。
- 日期处理：使用 `window.dayjs` 方法处理日期，功能与 `dayjs` 相同。
- 提示反馈：使用 `window.globalToast` 方法进行 toast 提示，参数为信息字符串。

4. 代码规范

- 注释说明：组件必须包含完备的注释，说明以下内容：组件名称，组件功能，技术栈要求，适用场景，使用示例等
- 代码风格：遵循业界优秀的 JavaScript 代码风格指南，保持代码的一致性和可读性

5. 其他

- 不要生成 `preview` 相关的代码。
- 不要生成与本组件无关的代码，当涉及到外部交互时，暴露相关 props 或事件。
- 组件入口文件为 `src/index.tsx`。组件的逻辑最终在入口文件进行聚合, 例如拼装子组件。