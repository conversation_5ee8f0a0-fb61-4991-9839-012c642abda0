{"name": "ainvest-matrix-react", "private": true, "version": "0.0.0", "scripts": {"build-test": "esno main.ts", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build"}, "dependencies": {"axios": "^1.8.4", "i18next": "^25.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.3", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.24.3", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/preset-env": "^7.24.3", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.24.1", "@babel/plugin-proposal-class-properties": "7", "@babel/plugin-proposal-export-default-from": "^7.25.9", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/runtime": "^7.26.7", "@chromatic-com/storybook": "^3.2.4", "@eslint/js": "^9.17.0", "@nuxt/friendly-errors-webpack-plugin": "^2.6.0", "@storybook/addon-essentials": "^8.6.0-alpha.1", "@storybook/addon-interactions": "^8.6.0-alpha.1", "@storybook/addon-onboarding": "^8.6.0-alpha.1", "@storybook/blocks": "^8.6.0-alpha.1", "@storybook/react": "^8.6.0-alpha.1", "@storybook/react-vite": "^8.6.0-alpha.1", "@storybook/test": "^8.6.0-alpha.1", "@types/node": "^22.13.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "babel-loader": "^8.2.5", "chokidar": "^3.5.3", "clean-webpack-plugin": "^4.0.0", "clsx": "^2.1.1", "core-js": "^3.36.1", "css-loader": "^3.6.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "eslint-plugin-storybook": "^0.11.2", "globals": "^15.14.0", "isomorphic-style-loader": "^5.4.0", "less": "^4.2.0", "less-loader": "^7.3.0", "null-loader": "^4.0.1", "ora": "^5.3.0", "postcss": "^8.5.1", "postcss-loader": "^4.3.0", "storybook": "^8.6.14", "style-loader": "^1.3.0", "tailwindcss": "^3.4.17", "typescript": "^5.4.3", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "webpack": "^4.47.0", "webpack-cli": "^3.3.12", "webpack-merge": "^5.10.0", "webpack-node-externals": "^3.0.0", "webpackbar": "^6.0.1"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}