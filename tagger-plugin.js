// src/componentTaggerPlugin.ts
import { parse } from '@babel/parser';
import fs from 'fs/promises';
import MagicString from 'magic-string';
import path2 from 'path';

// src/util.ts
import path from 'path';
import { existsSync } from 'fs';
function findProjectRoot(startPath = process.cwd()) {
  try {
    let currentPath = startPath;
    let count = 0;
    while (currentPath !== path.parse(currentPath).root && count < 20) {
      if (existsSync(path.join(currentPath, 'package.json'))) {
        return currentPath;
      }
      currentPath = path.dirname(currentPath);
      count++;
    }
    return process.cwd();
  } catch (error) {
    console.error('Error finding project root:', error);
    return process.cwd();
  }
}

var threeFiberElems = [
  'object3D', 'audioListener', 'positionalAudio', 'mesh', 'batchedMesh', 'instancedMesh',
  'scene', 'sprite', 'lOD', 'skinnedMesh', 'skeleton', 'bone', 'lineSegments', 'lineLoop',
  'points', 'group', 'camera', 'perspectiveCamera', 'orthographicCamera', 'cubeCamera',
  'arrayCamera', 'instancedBufferGeometry', 'bufferGeometry', 'boxBufferGeometry',
  'circleBufferGeometry', 'coneBufferGeometry', 'cylinderBufferGeometry', 
  'dodecahedronBufferGeometry', 'extrudeBufferGeometry', 'icosahedronBufferGeometry',
  'latheBufferGeometry', 'octahedronBufferGeometry', 'planeBufferGeometry',
  'polyhedronBufferGeometry', 'ringBufferGeometry', 'shapeBufferGeometry',
  'sphereBufferGeometry', 'tetrahedronBufferGeometry', 'torusBufferGeometry',
  'torusKnotBufferGeometry', 'tubeBufferGeometry', 'wireframeGeometry',
  'tetrahedronGeometry', 'octahedronGeometry', 'icosahedronGeometry',
  'dodecahedronGeometry', 'polyhedronGeometry', 'tubeGeometry', 'torusKnotGeometry',
  'torusGeometry', 'sphereGeometry', 'ringGeometry', 'planeGeometry', 'latheGeometry',
  'shapeGeometry', 'extrudeGeometry', 'edgesGeometry', 'coneGeometry', 'cylinderGeometry',
  'circleGeometry', 'boxGeometry', 'capsuleGeometry', 'material', 'shadowMaterial',
  'spriteMaterial', 'rawShaderMaterial', 'shaderMaterial', 'pointsMaterial',
  'meshPhysicalMaterial', 'meshStandardMaterial', 'meshPhongMaterial', 'meshToonMaterial',
  'meshNormalMaterial', 'meshLambertMaterial', 'meshDepthMaterial', 'meshDistanceMaterial',
  'meshBasicMaterial', 'meshMatcapMaterial', 'lineDashedMaterial', 'lineBasicMaterial',
  'primitive', 'light', 'spotLightShadow', 'spotLight', 'pointLight', 'rectAreaLight',
  'hemisphereLight', 'directionalLightShadow', 'directionalLight', 'ambientLight',
  'lightShadow', 'ambientLightProbe', 'hemisphereLightProbe', 'lightProbe',
  'spotLightHelper', 'skeletonHelper', 'pointLightHelper', 'hemisphereLightHelper',
  'gridHelper', 'polarGridHelper', 'directionalLightHelper', 'cameraHelper',
  'boxHelper', 'box3Helper', 'planeHelper', 'arrowHelper', 'axesHelper', 'texture',
  'videoTexture', 'dataTexture', 'dataTexture3D', 'compressedTexture', 'cubeTexture',
  'canvasTexture', 'depthTexture', 'raycaster', 'vector2', 'vector3', 'vector4',
  'euler', 'matrix3', 'matrix4', 'quaternion', 'bufferAttribute', 'float16BufferAttribute',
  'float32BufferAttribute', 'float64BufferAttribute', 'int8BufferAttribute',
  'int16BufferAttribute', 'int32BufferAttribute', 'uint8BufferAttribute',
  'uint16BufferAttribute', 'uint32BufferAttribute', 'instancedBufferAttribute',
  'color', 'fog', 'fogExp2', 'shape', 'colorShiftMaterial'
];

var dreiElems = [
  'AsciiRenderer', 'Billboard', 'Clone', 'ComputedAttribute', 'Decal', 'Edges', 'Effects',
  'GradientTexture', 'Image', 'MarchingCubes', 'Outlines', 'PositionalAudio', 'Sampler',
  'ScreenSizer', 'ScreenSpace', 'Splat', 'Svg', 'Text', 'Text3D', 'Trail', 'CubeCamera',
  'OrthographicCamera', 'PerspectiveCamera', 'CameraControls', 'FaceControls',
  'KeyboardControls', 'MotionPathControls', 'PresentationControls', 'ScrollControls',
  'DragControls', 'GizmoHelper', 'Grid', 'Helper', 'PivotControls', 'TransformControls',
  'CubeTexture', 'Fbx', 'Gltf', 'Ktx2', 'Loader', 'Progress', 'ScreenVideoTexture',
  'Texture', 'TrailTexture', 'VideoTexture', 'WebcamVideoTexture', 'CycleRaycast',
  'DetectGPU', 'Example', 'FaceLandmarker', 'Fbo', 'Html', 'Select', 'SpriteAnimator',
  'StatsGl', 'Stats', 'Trail', 'Wireframe', 'CurveModifier', 'AdaptiveDpr',
  'AdaptiveEvents', 'BakeShadows', 'Bvh', 'Detailed', 'Instances', 'Merged',
  'meshBounds', 'PerformanceMonitor', 'Points', 'Preload', 'Segments', 'Fisheye',
  'Hud', 'Mask', 'MeshPortalMaterial', 'RenderCubeTexture', 'RenderTexture', 'View',
  'MeshDiscardMaterial', 'MeshDistortMaterial', 'MeshReflectorMaterial',
  'MeshRefractionMaterial', 'MeshTransmissionMaterial', 'MeshWobbleMaterial',
  'PointMaterial', 'shaderMaterial', 'SoftShadows', 'CatmullRomLine', 'CubicBezierLine',
  'Facemesh', 'Line', 'Mesh', 'QuadraticBezierLine', 'RoundedBox', 'ScreenQuad',
  'AccumulativeShadows', 'Backdrop', 'BBAnchor', 'Bounds', 'CameraShake', 'Caustics',
  'Center', 'Cloud', 'ContactShadows', 'Environment', 'Float', 'Lightformer',
  'MatcapTexture', 'NormalTexture', 'RandomizedLight', 'Resize', 'ShadowAlpha',
  'Shadow', 'Sky', 'Sparkles', 'SpotLightShadow', 'SpotLight', 'Stage', 'Stars',
  'OrbitControls'
];

function shouldTagElement(elementName) {
  return !threeFiberElems.includes(elementName) && !dreiElems.includes(elementName);
}

// 安全转义HTML属性值的函数
function escapeHtmlAttribute(value) {
  if (typeof value !== 'string') {
    return String(value);
  }
  return value
    .replace(/&/g, '&amp;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/\n/g, ' ')
    .replace(/\r/g, ' ')
    .replace(/\t/g, ' ');
}

// 安全序列化JSON用于HTML属性
function safeJsonStringify(obj) {
  try {
    const jsonString = JSON.stringify(obj);
    if (!jsonString) {
      return '';
    }
    // 对JSON字符串进行HTML属性安全转义
    return escapeHtmlAttribute(jsonString);
  } catch (error) {
    console.warn('JSON stringify failed:', error);
    return '';
  }
}

// src/componentTaggerPlugin.ts
var validExtensions = /* @__PURE__ */ new Set(['.jsx', '.tsx']);
var projectRoot = findProjectRoot();
var outputLogFile = path2.resolve(projectRoot, '/.analysis.json');
var isSandbox = process.env.LOVABLE_DEV_SERVER === 'true';

// 日志记录相关变量
let logContent = '';
const MAX_LOG_SIZE = 1000000; // 字符上限

async function writeToLog(content) {
  if (logContent.length + content.length > MAX_LOG_SIZE) {
    return; // 超出限制，不再写入
  }
  
  logContent += content;
  
  try {
    await fs.writeFile(outputLogFile, logContent, 'utf8');
  } catch (error) {
    console.error('Error writing to output log:', error);
  }
}

// 分析文件内容的调试函数
function analyzeFileContent(code, fileName) {
  const lines = code.split('\n');
  const analysis = {
    totalLines: lines.length,
    firstFewLines: lines.slice(0, 25).map((line, index) => `[${index + 1}] ${line}`),
    emptyLinesAtStart: 0,
    hasImports: false,
    hasJSXImport: false,
    hasHMRCode: false,
    hasTypeAnnotations: false,
    encoding: 'utf-8', // 简单检测
    lineEndings: 'mixed' // 简单检测
  };

  // 检测开头的空行
  for (let i = 0; i < Math.min(lines.length, 30); i++) {
    if (lines[i].trim() === '') {
      analysis.emptyLinesAtStart++;
    } else {
      break;
    }
  }

  // 检测特殊内容
  const fullCode = code.toLowerCase();
  analysis.hasImports = fullCode.includes('import');
  analysis.hasJSXImport = fullCode.includes('react') || fullCode.includes('jsx');
  analysis.hasHMRCode = fullCode.includes('hmr') || fullCode.includes('hot') || fullCode.includes('refresh');
  analysis.hasTypeAnnotations = fullCode.includes(': ') || fullCode.includes('interface') || fullCode.includes('type ');

  // 检测换行符类型
  if (code.includes('\r\n')) {
    analysis.lineEndings = 'CRLF';
  } else if (code.includes('\n')) {
    analysis.lineEndings = 'LF';
  } else if (code.includes('\r')) {
    analysis.lineEndings = 'CR';
  }

  return analysis;
}

// 检测 HMR 代码注入并计算行号偏移
function calculateLineOffset(sourceCode, compiledCode) {
  const sourceLines = sourceCode.split('\n');
  const compiledLines = compiledCode.split('\n');
  let debugLog = '';
  
  debugLog += '\n=== 行号偏移计算 ===\n';
  debugLog += '源文件行数: ' + sourceLines.length + '\n';
  debugLog += '编译后行数: ' + compiledLines.length + '\n';
  
  // 记录源文件前20行
  debugLog += '\n源文件前20行:\n';
  sourceLines.slice(0, 20).forEach((line, i) => {
    debugLog += '[' + (i + 1) + '] ' + (line.trim() || '(空行)') + '\n';
  });
  
  // 记录编译后文件前20行
  debugLog += '\n编译后文件前20行:\n';
  compiledLines.slice(0, 20).forEach((line, i) => {
    debugLog += '[' + (i + 1) + '] ' + (line.trim() || '(空行)') + '\n';
  });

  // 检测 HMR 代码结束位置
  const hmrPatterns = [
    /import \* as RefreshRuntime from/,
    /const inWebWorker = typeof WorkerGlobalScope/,
    /if \(import\.meta\.hot && !inWebWorker\)/,
    /window\.\$RefreshReg\$/,
    /window\.\$RefreshSig\$/,
    /RefreshRuntime\.getRefreshReg/,
    /RefreshRuntime\.createSignatureFunctionForTransform/,
    /var _s = \$RefreshSig\$\(\)/
  ];

  let hmrEndLine = 0;
  for (let i = 0; i < Math.min(compiledLines.length, 50); i++) {
    const line = compiledLines[i].trim();
    if (line === '') continue;
    
    const isHMRLine = hmrPatterns.some(pattern => pattern.test(line));
    if (isHMRLine) {
      hmrEndLine = i + 1;
      debugLog += '[HMR] 在第 ' + (i + 1) + ' 行发现HMR代码: ' + line + '\n';
    }
  }

  debugLog += '\nHMR代码结束位置: ' + hmrEndLine + '\n';

  // 计算真实的行号偏移：编译后文件行数 - 源文件行数 - HMR代码行数
  const offset = compiledLines.length - sourceLines.length - hmrEndLine;
  debugLog += '计算行号偏移:\n';
  debugLog += '编译后行数: ' + compiledLines.length + '\n';
  debugLog += '源文件行数: ' + sourceLines.length + '\n';
  debugLog += 'HMR代码行数: ' + hmrEndLine + '\n';
  debugLog += '最终偏移量: ' + offset + '\n';
  
  return { offset, debugLog, hmrEndLine };
}

// 简单的 AST 遍历函数，替代 estree-walker
function walkAST(node, visitor) {
  if (!node || typeof node !== 'object') return;
  
  let nodeDebugInfo = '';
  
  if (node.type === 'JSXElement') {
    const openingElement = node.openingElement;
    const closingElement = node.closingElement;
    const isSelfClosing = openingElement.selfClosing;
    
    // 获取原始位置信息
    const rawLineStart = openingElement.loc?.start?.line ?? 0;
    const rawLineEnd = closingElement?.loc?.end?.line ?? rawLineStart;
    const col = openingElement.loc?.start?.column ?? 0;

    // 使用行号偏移计算真实行号（减去 HMR 代码的行数）
    const adjustedLineStart = Math.max(1, rawLineStart - visitor.hmrEndLine);
    const adjustedLineEnd = Math.max(1, rawLineEnd - visitor.hmrEndLine);

    // 记录节点处理的详细信息
    nodeDebugInfo += '=== 开始解析 JSX 节点 ===\n';
    nodeDebugInfo += '1. 节点基本信息:\n';
    nodeDebugInfo += '类型: ' + node.type + '\n';
    nodeDebugInfo += '元素名: ' + openingElement.name.name + '\n';
    nodeDebugInfo += '位置: 行 ' + rawLineStart + ' 列 ' + col + 
                    ' 到 行 ' + rawLineEnd + ' 列 ' + (closingElement?.loc?.end?.column ?? 0) + '\n';
    nodeDebugInfo += '是否自闭合: ' + (isSelfClosing ? 'true' : 'false') + '\n\n';
    
    nodeDebugInfo += '2. 源代码上下文:\n';
    const contextStart = Math.max(0, rawLineStart - 3);
    const contextEnd = Math.min(visitor.lines.length, rawLineEnd + 3);
    for (let i = contextStart; i <= contextEnd; i++) {
      const lineContent = visitor.lines[i - 1] || '';
      const prefix = i === rawLineStart ? '> ' : '  ';
      nodeDebugInfo += `${prefix}[${i}] ${lineContent}\n`;
    }
    nodeDebugInfo += '\n';

    nodeDebugInfo += '3. 节点内容:\n';
    nodeDebugInfo += '前一行内容: ' + (visitor.lines[rawLineStart - 2]?.trim() || '(空行)') + '\n';
    nodeDebugInfo += '当前内容: ' + (visitor.lines[rawLineStart - 1]?.trim() || '(空行)') + '\n';
    nodeDebugInfo += '后一行内容: ' + (visitor.lines[rawLineStart]?.trim() || '(空行)') + '\n\n';

    nodeDebugInfo += '4. AST 节点信息:\n';
    nodeDebugInfo += JSON.stringify({
      type: node.type,
      name: openingElement.name.name,
      location: {
        start: openingElement.loc?.start,
        end: closingElement?.loc?.end
      },
      selfClosing: isSelfClosing,
      attributes: openingElement.attributes.map(attr => ({
        name: attr.name?.name,
        value: attr.value?.type === 'StringLiteral' ? attr.value.value : 
               attr.value?.type === 'JSXExpressionContainer' ? 'Expression' : 'Unknown'
      }))
    }, null, 2).replace(/"/g, '\"') + '\n\n';

    nodeDebugInfo += '5. 行号计算:\n';
    nodeDebugInfo += '原始起始行: ' + rawLineStart + '\n';
    nodeDebugInfo += '原始结束行: ' + rawLineEnd + '\n';
    nodeDebugInfo += 'HMR代码行数: ' + visitor.hmrEndLine + '\n';
    nodeDebugInfo += '调整后起始行: ' + adjustedLineStart + '\n';
    nodeDebugInfo += '调整后结束行: ' + adjustedLineEnd + '\n';
    nodeDebugInfo += '总行号偏移量: ' + visitor.lineOffset + '\n\n';

    nodeDebugInfo += '6. 父节点信息:\n';
    if (node.parent) {
      nodeDebugInfo += JSON.stringify({
        type: node.parent.type,
        name: node.parent.type === 'JSXElement' ? 
              node.parent.openingElement?.name?.name : 'non-element',
        location: node.parent.loc
      }, null, 2).replace(/"/g, '\"') + '\n';
    } else {
      nodeDebugInfo += '无父节点\n';
    }
    nodeDebugInfo += '\n';

    // 处理属性
    const attributes = openingElement.attributes.reduce((acc, attr) => {
      if (attr.type === 'JSXAttribute') {
        if (attr.value?.type === 'StringLiteral') {
          acc[attr.name.name] = attr.value.value;
        } else if (attr.value?.type === 'JSXExpressionContainer' && attr.value.expression.type === 'StringLiteral') {
          acc[attr.name.name] = attr.value.expression.value;
        }
      }
      return acc;
    }, {});

    // 处理文本内容
    let textContent = '';
    if (node.children) {
      textContent = node.children
        .map(child => {
          if (child.type === 'JSXText') {
            return child.value.trim();
          } else if (child.type === 'JSXExpressionContainer') {
            if (child.expression.type === 'StringLiteral') {
              return child.expression.value;
            }
          }
          return '';
        })
        .filter(Boolean)
        .join(' ')
        .trim();
    }

    // 构建内容对象
    const content = {};
    if (textContent) {
      content.text = textContent;
    }
    if (attributes.placeholder) {
      content.placeholder = attributes.placeholder;
    }
    if (attributes.className) {
      content.className = attributes.className;
    }

    const dataComponentId = visitor.filePath + ':' + adjustedLineStart + ':' + col;

    if (shouldTagElement(openingElement.name.name)) {
      // 使用安全的属性值构建方式
      const safeRelativePath = escapeHtmlAttribute(visitor.filePath);
      const safeFileName = escapeHtmlAttribute(visitor.fileName);
      const safeElementName = escapeHtmlAttribute(openingElement.name.name);
      const safeDataComponentId = escapeHtmlAttribute(dataComponentId);
      const safeContentJson = safeJsonStringify(content);

      // 构建安全的属性字符串
      const attributes = [
        'data-component-id="' + safeDataComponentId + '"',
        'data-component-path="' + safeRelativePath + '"',
        'data-component-line-start="' + adjustedLineStart + '"',
        'data-component-line-end="' + adjustedLineEnd + '"',
        'data-component-file="' + safeFileName + '"',
        'data-component-name="' + safeElementName + '"',
        safeContentJson ? 'data-component-content="' + safeContentJson + '"' : ''
      ].filter(Boolean);

      const attributesString = ' ' + attributes.join(' ');
      visitor.nodeInfo = visitor.nodeInfo.replace('[源代码内容占位符]', nodeDebugInfo);
      visitor.nodeInfo = visitor.nodeInfo.replace('[代码片段占位符]', nodeDebugInfo);
      visitor.nodeInfo += '\n' + attributesString;
      visitor.nodeInfo += '\n' + nodeDebugInfo;
      visitor.nodeInfo += '✅ 已添加标记（起始行: ' + adjustedLineStart + ', 结束行: ' + adjustedLineEnd + '）\n';
      visitor.nodeInfo += '   属性字符串: ' + attributesString + '\n';
      visitor.magicString.appendLeft(openingElement.name.end ?? 0, attributesString);
      visitor.changedElementsCount++;
    } else {
      visitor.nodeInfo += '\n❌ 跳过标记（在排除列表中）\n';
    }
  }
  
  visitor.nodeInfo = visitor.nodeInfo || '';
  visitor.nodeInfo += nodeDebugInfo;
  
  for (const key in node) {
    if (key === 'parent' || key === 'leadingComments' || key === 'trailingComments') continue;
    
    const child = node[key];
    if (Array.isArray(child)) {
      child.forEach(item => walkAST(item, visitor));
    } else if (child && typeof child === 'object' && child.type) {
      walkAST(child, visitor);
    }
  }
}

// 新增：外部组件追踪相关变量
let externalComponentMap = {};
// 全局唯一外部组件 usage 数组
let externalComponentsUsage = [];

function componentTagger() {
  const cwd = process.cwd();
  const stats = {
    totalFiles: 0,
    processedFiles: 0,
    totalElements: 0,
  };

  return {
    name: 'vite-plugin-component-tagger',
    enforce: 'pre',
    async transform(code, id) {
      if (!validExtensions.has(path2.extname(id)) || id.includes('node_modules')) {
        return null;
      }
      const relativePath = path2.relative(cwd, id);
      const fileName = path2.basename(id);
      let sourceCode;
      try {
        sourceCode = await fs.readFile(id, 'utf-8');
      } catch (error) {
        return null;
      }
      try {
        const ast = parse(code, {
          sourceType: 'module',
          plugins: ['jsx', 'typescript'],
        });
        function isExternalPkg(source) {
          return source && !source.startsWith('.') && !source.startsWith('/');
        }
        let externalComponentMap = {};
        if (ast && ast.program && ast.program.body) {
          for (var i = 0; i < ast.program.body.length; i++) {
            var node = ast.program.body[i];
            if (node.type === 'ImportDeclaration') {
              for (var j = 0; j < node.specifiers.length; j++) {
                var spec = node.specifiers[j];
                if (isExternalPkg(node.source.value)) {
                  if (spec.type === 'ImportSpecifier' || spec.type === 'ImportDefaultSpecifier') {
                    externalComponentMap[spec.local.name] = node.source.value;
                  } else if (spec.type === 'ImportNamespaceSpecifier') {
                    externalComponentMap[spec.local.name] = node.source.value + '.*';
                  }
                }
              }
            }
          }
        }
        // MagicString 实例
        const magicString = new MagicString(code);
        // walkAST 递归，收集 external usage 并生成 analysis 树，同时注入属性
        function walkAST(node, visitor) {
          if (!node || typeof node !== 'object') return null;
          let result = { type: node.type };
          if (node.type === 'JSXElement') {
            let openingElement = node.openingElement;
            let tagName = '';
            if (openingElement.name.type === 'JSXIdentifier') {
              tagName = openingElement.name.name;
            } else if (openingElement.name.type === 'JSXMemberExpression') {
              tagName = (openingElement.name.object.name || '[object]') + '.' + (openingElement.name.property.name || '[property]');
            } else if (openingElement.name.type === 'JSXNamespacedName') {
              tagName = openingElement.name.namespace.name + ':' + openingElement.name.name.name;
            }
            result.name = tagName;
            result.loc = openingElement.loc;
            result.attrs = (openingElement.attributes || []).map(a => a.name && a.name.name ? { name: a.name.name } : {});
            let isExternalComponent = !!externalComponentMap[tagName];
            // 构建需要注入的属性
            let attrsToInject = [];
            // data-component-xxx
            const lineStart = openingElement.loc && openingElement.loc.start && openingElement.loc.start.line;
            const lineEnd = node.closingElement && node.closingElement.loc && node.closingElement.loc.end && node.closingElement.loc.end.line;
            const col = openingElement.loc && openingElement.loc.start && openingElement.loc.start.column;
            const dataComponentId = relativePath + ':' + lineStart + ':' + col;
            attrsToInject.push('data-component-id="' + dataComponentId + '"');
            attrsToInject.push('data-component-path="' + relativePath + '"');
            attrsToInject.push('data-component-line-start="' + lineStart + '"');
            attrsToInject.push('data-component-line-end="' + lineEnd + '"');
            attrsToInject.push('data-component-file="' + fileName + '"');
            attrsToInject.push('data-component-name="' + tagName + '"');
            // 额外内容属性
            let content = {};
            if (openingElement.attributes) {
              for (const attr of openingElement.attributes) {
                if (attr.type === 'JSXAttribute' && attr.name && attr.value && attr.value.type === 'StringLiteral') {
                  content[attr.name.name] = attr.value.value;
                }
              }
            }
            if (Object.keys(content).length > 0) {
              attrsToInject.push('data-component-content="' + encodeURIComponent(JSON.stringify(content)) + '"');
            }
            // 外部组件属性
            if (isExternalComponent) {
              result.external = { pkg: externalComponentMap[tagName] };
              attrsToInject.push('data-external-pkg="' + externalComponentMap[tagName] + '"');
              attrsToInject.push('data-external-pkg-source-path="' + relativePath + '"');
              attrsToInject.push('data-external-pkg-source-line="' + lineStart + '"');
              externalComponentsUsage.push({
                component: tagName,
                pkg: externalComponentMap[tagName],
                file: visitor.filePath,
                line: lineStart,
                col: col
              });
            }
            // 注入属性到源码
            if (openingElement && openingElement.name && openingElement.name.end !== undefined) {
              magicString.appendLeft(openingElement.name.end, ' ' + attrsToInject.join(' '));
            }
          }
          // 递归子节点
          for (let key in node) {
            if (key === 'parent' || key === 'leadingComments' || key === 'trailingComments') continue;
            let child = node[key];
            if (Array.isArray(child)) {
              let arr = [];
              for (let k = 0; k < child.length; k++) {
                let sub = walkAST(child[k], visitor);
                if (sub) arr.push(sub);
              }
              if (arr.length) result[key] = arr;
            } else if (child && typeof child === 'object' && child.type) {
              let sub = walkAST(child, visitor);
              if (sub) result[key] = sub;
            }
          }
          return result;
        }
        const visitor = { filePath: relativePath, fileName: fileName, externalComponentMap };
        const analysisTree = walkAST(ast, visitor);
        // 4. 写 .ast-tree.json（移除 parent 字段防止循环引用）
        var astCopy = JSON.parse(JSON.stringify(ast, function(key, value) {
          if (key === 'parent') return undefined;
          return value;
        }));
        const astJsonFile = path2.resolve(projectRoot, '/.ast-tree.json');
        try {
          await fs.writeFile(astJsonFile, JSON.stringify(astCopy, null, 2), 'utf8');
        } catch {}
        // 5. 写 .analysis.json（全量树结构，包含解析结果）
        const analysisJsonFile = path2.resolve(projectRoot, '/.analysis.json');
        try {
          await fs.writeFile(analysisJsonFile, JSON.stringify(analysisTree, null, 2), 'utf8');
        } catch {}
        // 6. 写 .external-components.json（基于全局 externalComponentsUsage）
        const externalJsonFile = path2.resolve(projectRoot, '/.external-components.json');
        try {
          const info = {
            generatedAt: new Date().toISOString(),
            totalExternalComponents: externalComponentsUsage.length,
            message: externalComponentsUsage.length > 0
              ? '已收集到外部组件依赖信息。'
              : '未检测到外部组件依赖，但已生成日志文件用于调试。',
            usage: externalComponentsUsage
          };
          await fs.writeFile(
            externalJsonFile,
            JSON.stringify(info, null, 2),
            'utf8'
          );
        } catch {}
        // 返回注入属性后的代码
        return { code: magicString.toString(), map: magicString.generateMap({ hires: true }) };
      } catch (error) {
        return null;
      }
    },
    async buildEnd() {
      const externalJsonFile = path2.resolve(projectRoot, '/.external-components.json');
      try {
        const info = {
          generatedAt: new Date().toISOString(),
          totalExternalComponents: externalComponentsUsage.length,
          message: externalComponentsUsage.length > 0
            ? '已收集到外部组件依赖信息。'
            : '未检测到外部组件依赖，但已生成日志文件用于调试。',
          usage: externalComponentsUsage
        };
        await fs.writeFile(
          externalJsonFile,
          JSON.stringify(info, null, 2),
          'utf8'
        );
      } catch {}
    }
  };
}

// 添加辅助函数
function getElementDepth(element) {
  let depth = 0;
  let current = element;
  while (current?.parent) {
    depth++;
    current = current.parent;
  }
  return depth;
}

function getParentElementInfo(element) {
  if (!element?.parent) return 'none';
  if (element.parent.type === 'JSXElement' && element.parent.openingElement?.name?.name) {
    return element.parent.openingElement.name.name;
  }
  return 'unknown';
}

export { componentTagger };